# 📰 Pravasi ePaper – Project Plan & Module Overview

## 📁 Workspace Structure

```
workspace/
├── admin-dashboard/         # React + Notus React (Admin Panel)
├── api/                     # FastAPI backend
├── pravasi-epaper/          # React + TypeScript + Vite (Frontend)
└── project-documents/       # Documentation and guides
```

---

## 🧱 Project Overview

A subscription-based ePaper platform allowing users to read digital newspapers securely **without download**, offering **half-page previews** to non-subscribers. Only logged-in subscribers can read full editions via a secure viewer.

---

## 🔧 Technologies

| Layer       | Stack                                 |
| ----------- | ------------------------------------- |
| Frontend    | React + TypeScript + Vite             |
| Backend     | FastAPI                               |
| Admin Panel | Notus React Template                  |
| Auth        | JWT (FastAPI Users / PyJWT)           |
| Viewer      | PDF.js (browser-secured)              |
| Database    | SQLite for dev → PostgreSQL for prod  |
| Storage     | Local for dev → S3 or similar in prod |

---

## 📦 Core Modules

### 1. 👤 User Module (`api/users`)

* User Registration
* Login (JWT-based)
* Profile endpoint
* Token refresh / session control

### 2. 💳 Subscription & Plans Module (`api/subscriptions`)

* Create & manage plans (monthly, yearly, trial)
* User subscription mapping
* Check active subscription status
* Payment gateway integration placeholder (Stripe/Razorpay)

### 3. 📄 ePaper Module (`api/epaper`)

* Upload and store PDF files (admin-only)
* Store metadata (title, publish date)
* Auto-generate:

  * First-page preview (cropped to 50%)
  * Thumbnail (optional)
* Serve PDFs securely using signed or temporary-access endpoints

### 4. 🔐 Secure PDF Viewer (`pravasi-epaper`)

* Use customized PDF.js in React
* Prevent:

  * Right-click
  * Print
  * Download
* Inject watermark (email, IP, timestamp)
* Session-based token validation for streaming PDFs

### 5. 👁️ Public Preview Module (`pravasi-epaper`)

* For non-authenticated users:

  * Render **half-page** preview of first page
  * CTA prompting to subscribe or log in
* Fully disabled right-click and interactions

---

## 🖥️ Admin Dashboard (`admin-dashboard`)

* Built using Notus React template
* Roles: Admin only
* Features:

  * Upload daily papers
  * Manage users
  * Manage subscription plans
  * View usage statistics
  * Preview uploaded PDFs
* Routes:

  * `/dashboard`
  * `/upload-paper`
  * `/manage-users`
  * `/plans`
  * `/analytics`

---

## 🌐 Frontend App (`pravasi-epaper`)

* Built with React + TypeScript + Vite
* Public routes:

  * Home
  * Browse ePapers
  * First-page previews
  * Login/Register
* Protected routes:

  * Full PDF viewer (only if subscription is active)
  * Profile
  * Archived editions

---

## 🔒 PDF Security Measures

* Render PDFs via PDF.js (no direct PDF URL exposure)
* Signed token-based view access (e.g., `/api/epaper/view/{id}?token=xyz`)
* Disable all standard browser controls (via custom JS)
* Inject watermarks dynamically per session/user
* Optional: render page-by-page as images for stronger DRM

---

## 🔧 Backend Services (`api/`)

* FastAPI project with routers:

  ```
  api/
  ├── main.py
  ├── routers/
  │   ├── users.py
  │   ├── auth.py
  │   ├── subscriptions.py
  │   ├── epaper.py
  └── models/
  ```
* Dependencies:

  * `fastapi`
  * `uvicorn`
  * `sqlalchemy`
  * `python-multipart`
  * `PyJWT` or `fastapi-users` (optional)
* Media storage (dev): `media/` folder locally

---

## 🧪 Development Notes

* Use SQLite during development (PostgreSQL in production)
* Use `uvicorn main:app --reload` to run backend
* Use `vite` or `npm run dev` to run frontend
* Use proxy configuration or CORS for frontend-backend API integration

---

## 🔜 Optional Future Enhancements

* Device/session restriction per user
* Analytics dashboard for engagement tracking
* Email notifications for new editions
* OTP-based login (via phone/email)
* Mobile PWA version
* Auto-cleanup of old previews

---

## ✅ Next Steps

1. Backend

   * Define DB models (users, subscriptions, papers)
   * Implement JWT auth & permissions
   * Add file upload + preview generation logic

2. Frontend

   * Build auth flow (login/register)
   * Create public preview viewer
   * Implement secure viewer for full access

3. Admin

   * Add upload panel and plan manager
   * Display list of uploaded editions
   * User list with subscription status
