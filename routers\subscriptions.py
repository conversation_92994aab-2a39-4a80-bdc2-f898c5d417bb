from typing import List, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel
from decimal import Decimal

from database.database import get_db
from models.user import User
from models.subscription import Plan, UserSubscription, PlanType, SubscriptionStatus as SubStatus
from dependencies.auth import get_current_active_user, get_admin_user

router = APIRouter(prefix="/subscriptions", tags=["subscriptions"])

# Pydantic models
class PlanCreate(BaseModel):
    name: str
    description: Optional[str] = None
    plan_type: PlanType
    price: float
    duration_days: int
    features: Optional[str] = None

class PlanUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    price: Optional[float] = None
    duration_days: Optional[int] = None
    is_active: Optional[bool] = None
    features: Optional[str] = None

class PlanResponse(BaseModel):
    id: int
    name: str
    description: Optional[str]
    plan_type: PlanType
    price: float
    duration_days: int
    is_active: bool
    features: Optional[str]
    
    class Config:
        from_attributes = True

class SubscriptionCreate(BaseModel):
    plan_id: int
    payment_id: Optional[str] = None

class SubscriptionResponse(BaseModel):
    id: int
    user_id: int
    plan_id: int
    status: SubscriptionStatus
    start_date: datetime
    end_date: datetime
    auto_renew: bool
    plan: PlanResponse
    
    class Config:
        from_attributes = True

class SubscriptionStatus(BaseModel):
    is_active: bool
    current_plan: Optional[PlanResponse]
    days_remaining: int
    expires_at: Optional[datetime]

# Public endpoints
@router.get("/plans", response_model=List[PlanResponse])
async def get_available_plans(
    plan_type: Optional[PlanType] = Query(None),
    db: Session = Depends(get_db)
):
    """Get all available subscription plans."""
    query = db.query(Plan).filter(Plan.is_active == True)
    
    if plan_type:
        query = query.filter(Plan.plan_type == plan_type)
    
    plans = query.all()
    return plans

@router.get("/status")
async def get_subscription_status(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get current user's subscription status."""
    # Get active subscription
    active_subscription = db.query(UserSubscription).filter(
        UserSubscription.user_id == current_user.id,
        UserSubscription.status == SubscriptionStatus.ACTIVE,
        UserSubscription.end_date > datetime.utcnow()
    ).first()
    
    if active_subscription:
        return {
            "is_active": True,
            "current_plan": active_subscription.plan,
            "days_remaining": active_subscription.days_remaining,
            "expires_at": active_subscription.end_date
        }
    else:
        return {
            "is_active": False,
            "current_plan": None,
            "days_remaining": 0,
            "expires_at": None
        }

@router.post("/subscribe", response_model=SubscriptionResponse)
async def subscribe_to_plan(
    subscription_data: SubscriptionCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Subscribe user to a plan."""
    # Check if plan exists
    plan = db.query(Plan).filter(Plan.id == subscription_data.plan_id).first()
    if not plan:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Plan not found"
        )
    
    if not plan.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Plan is not available"
        )
    
    # Check if user already has an active subscription
    existing_subscription = db.query(UserSubscription).filter(
        UserSubscription.user_id == current_user.id,
        UserSubscription.status == SubscriptionStatus.ACTIVE,
        UserSubscription.end_date > datetime.utcnow()
    ).first()
    
    if existing_subscription:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User already has an active subscription"
        )
    
    # Create new subscription
    start_date = datetime.utcnow()
    end_date = start_date + timedelta(days=plan.duration_days)
    
    new_subscription = UserSubscription(
        user_id=current_user.id,
        plan_id=plan.id,
        status=SubscriptionStatus.ACTIVE if plan.is_trial else SubscriptionStatus.PENDING,
        start_date=start_date,
        end_date=end_date,
        payment_id=subscription_data.payment_id
    )
    
    db.add(new_subscription)
    db.commit()
    db.refresh(new_subscription)
    
    return new_subscription

@router.get("/my-subscriptions", response_model=List[SubscriptionResponse])
async def get_my_subscriptions(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get current user's subscription history."""
    subscriptions = db.query(UserSubscription).filter(
        UserSubscription.user_id == current_user.id
    ).order_by(UserSubscription.created_at.desc()).all()
    
    return subscriptions

@router.post("/cancel/{subscription_id}")
async def cancel_subscription(
    subscription_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Cancel a subscription."""
    subscription = db.query(UserSubscription).filter(
        UserSubscription.id == subscription_id,
        UserSubscription.user_id == current_user.id
    ).first()
    
    if not subscription:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Subscription not found"
        )
    
    if subscription.status != SubscriptionStatus.ACTIVE:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Subscription is not active"
        )
    
    subscription.cancel_subscription()
    db.commit()
    
    return {"message": "Subscription cancelled successfully"}

# Admin endpoints
@router.post("/plans", response_model=PlanResponse)
async def create_plan(
    plan_data: PlanCreate,
    admin_user: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """Create a new subscription plan (admin only)."""
    new_plan = Plan(
        name=plan_data.name,
        description=plan_data.description,
        plan_type=plan_data.plan_type,
        price=plan_data.price,
        duration_days=plan_data.duration_days,
        features=plan_data.features
    )
    
    db.add(new_plan)
    db.commit()
    db.refresh(new_plan)
    
    return new_plan

@router.put("/plans/{plan_id}", response_model=PlanResponse)
async def update_plan(
    plan_id: int,
    plan_update: PlanUpdate,
    admin_user: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """Update a subscription plan (admin only)."""
    plan = db.query(Plan).filter(Plan.id == plan_id).first()
    if not plan:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Plan not found"
        )
    
    # Update fields
    if plan_update.name is not None:
        plan.name = plan_update.name
    if plan_update.description is not None:
        plan.description = plan_update.description
    if plan_update.price is not None:
        plan.price = plan_update.price
    if plan_update.duration_days is not None:
        plan.duration_days = plan_update.duration_days
    if plan_update.is_active is not None:
        plan.is_active = plan_update.is_active
    if plan_update.features is not None:
        plan.features = plan_update.features
    
    db.commit()
    db.refresh(plan)
    
    return plan

@router.get("/admin/subscriptions", response_model=List[SubscriptionResponse])
async def list_all_subscriptions(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    status: Optional[SubscriptionStatus] = Query(None),
    admin_user: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """List all subscriptions (admin only)."""
    query = db.query(UserSubscription)
    
    if status:
        query = query.filter(UserSubscription.status == status)
    
    subscriptions = query.offset(skip).limit(limit).all()
    return subscriptions
